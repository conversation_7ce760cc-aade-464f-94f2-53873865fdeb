'use client';

import React, { useEffect, useState, useRef } from "react";

interface ClientAITaxonomistWrapperProps {
  apiKey?: string;
  apiUrl?: string;
  onResults?: (results: any) => void;
  onTopResult?: (result: any) => void;
  enableOverlays?: boolean;
}

export default function ClientAITaxonomistWrapper({
  apiKey = "2b10ZGZjBNw01mCeNDfdCByQe",
  apiUrl = "https://my-api.plantnet.org/v2/identify/all",
  onResults,
  onTopResult,
  enableOverlays = false
}: ClientAITaxonomistWrapperProps) {
  const [topResult, setTopResult] = useState<any>(null);
  const [isClient, setIsClient] = useState(false);
  const [overlayData, setOverlayData] = useState<Array<{
    rect: DOMRect;
    result: any;
    index: number;
  }>>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Function to detect and create overlay data for plant entries
  const detectPlantEntryLayout = () => {
    if (!enableOverlays || !containerRef.current) {
      console.log('Overlay detection skipped:', { enableOverlays, hasContainer: !!containerRef.current });
      return;
    }

    const component = containerRef.current.querySelector('ai-taxonomist') as any;
    if (!component?.shadowRoot) {
      console.log('No component or shadow root found');
      return;
    }

    // Look for the results list in the shadow DOM
    const taxonResults = component.shadowRoot.querySelector('taxon-results');
    if (!taxonResults?.shadowRoot) {
      console.log('No taxon-results component or shadow root found');
      return;
    }

    // Look specifically for the results container, not just any ul
    const resultsContainer = taxonResults.shadowRoot.querySelector('.container');
    if (!resultsContainer) {
      console.log('No results container found');
      return;
    }

    // Look for the results list, but make sure it's not in the image picker area
    const resultsList = resultsContainer.querySelector('ul');
    if (!resultsList) {
      console.log('No results list found in results container');
      return;
    }

    // Additional check: make sure we're looking at the actual results section
    // The results section should have a title or be after the image picker
    const titleElement = resultsContainer.querySelector('.title');
    if (titleElement) {
      console.log('Found title element:', titleElement.textContent);
    }

    // Get all result entries - these should be the actual plant results
    // First, let's see what's actually in the results list
    console.log('Results list HTML:', resultsList.innerHTML.substring(0, 500));

    const allResultEntries = resultsList.querySelectorAll('li.result');
    console.log('All result entries found:', allResultEntries.length);

    // Let's also check if there are any other selectors we should use
    const allLiElements = resultsList.querySelectorAll('li');
    console.log('All li elements found:', allLiElements.length);

    // Check each li element to understand the structure
    allLiElements.forEach((li: Element, index: number) => {
      console.log(`Li ${index}:`, {
        classList: Array.from(li.classList),
        hasResult: li.classList.contains('result'),
        innerHTML: li.innerHTML.substring(0, 200) + '...'
      });
    });

    // Additional filtering to ensure we only get plant result entries
    const resultEntries = Array.from(allResultEntries).filter((entry, index) => {
      const element = entry as Element;

      // Check if this entry has the typical plant result structure
      const hasScore = element.querySelector('.score');
      const hasSpecies = element.querySelector('.species');
      const hasFamily = element.querySelector('.family');
      const hasImages = element.querySelector('.imgContainer');

      // Additional checks to exclude search image area
      const scoreText = hasScore ? hasScore.textContent?.trim() : '';
      const speciesText = hasSpecies ? hasSpecies.textContent?.trim() : '';

      // Check if this looks like a search image entry (usually has no score percentage)
      const hasValidScore = scoreText && scoreText.includes('%') && !scoreText.includes('Original image');
      const hasValidSpecies = speciesText && speciesText.length > 0 && !speciesText.includes('Original image');

      // Look for specific indicators that this is NOT a search image
      const isNotSearchImage = !element.innerHTML.includes('Original image') &&
                              !element.innerHTML.includes('image(s)') &&
                              !element.innerHTML.includes('Plant 1:') && // Exclude our own overlay tooltips
                              hasValidScore &&
                              hasValidSpecies;

      // Additional check: if this is the first entry and it doesn't have a valid score,
      // it's likely the search image
      const isFirstEntryWithoutScore = index === 0 && !hasValidScore;

      // Final check: exclude if it looks like an image picker or search result
      const looksLikeSearchImage = element.innerHTML.includes('image-selected') ||
                                  element.innerHTML.includes('image-picker') ||
                                  isFirstEntryWithoutScore;

      console.log(`Entry ${index} structure check:`, {
        hasScore: !!hasScore,
        hasSpecies: !!hasSpecies,
        hasFamily: !!hasFamily,
        hasImages: !!hasImages,
        scoreText,
        speciesText,
        hasValidScore,
        hasValidSpecies,
        isNotSearchImage,
        isFirstEntryWithoutScore,
        looksLikeSearchImage,
        classList: Array.from(element.classList),
        innerHTML: element.innerHTML.substring(0, 300) + '...'
      });

      // Must have all the key components of a plant result AND not be a search image
      return hasScore && hasSpecies && hasFamily && isNotSearchImage && !looksLikeSearchImage;
    });

    if (resultEntries.length === 0) {
      console.log('No valid result entries found after filtering');
      return;
    }

    // Get current results data
    const currentResults = component.identify?.results?.results || [];
    if (currentResults.length === 0) {
      console.log('No current results data');
      return;
    }

    console.log('Found', resultEntries.length, 'valid result entries and', currentResults.length, 'result data items');

    // Map the filtered entries to overlay data
    // Since we've filtered out invalid entries, we need to map them correctly to the results data
    const newOverlayData = resultEntries.map((entry, filteredIndex: number) => {
      const element = entry as Element;

      // Find the species column (second column) within this result entry
      const speciesColumn = element.querySelector('.species');
      if (!speciesColumn) {
        console.log(`No species column found for entry ${filteredIndex}`);
        return null;
      }

      // Get the bounding rect of just the species column
      const rect = speciesColumn.getBoundingClientRect();

      // Use fixed positioning with viewport coordinates
      // This is simpler and more reliable than relative positioning
      const fixedRect = new DOMRect(
        rect.left,
        rect.top,
        rect.width,
        rect.height
      );

      console.log(`Species column position for entry ${filteredIndex}:`, {
        speciesColumnRect: { x: rect.x, y: rect.y, left: rect.left, top: rect.top, width: rect.width, height: rect.height },
        fixedRect: { x: fixedRect.x, y: fixedRect.y, width: fixedRect.width, height: fixedRect.height },
        speciesColumnContent: speciesColumn.innerHTML.substring(0, 150) + '...'
      });

      // Map to the correct result data - use filteredIndex since we've already filtered
      const resultData = currentResults[filteredIndex];

      console.log(`Valid Species Column ${filteredIndex}:`, {
        rect: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
        fixedRect: { x: fixedRect.x, y: fixedRect.y, width: fixedRect.width, height: fixedRect.height },
        result: resultData?.species?.scientificNameWithoutAuthor || resultData?.taxonName,
        originalIndex: filteredIndex,
        totalResults: currentResults.length,
        targetingSpeciesColumn: true
      });

      // Additional debugging: log the actual element position
      console.log(`Element ${filteredIndex} position details:`, {
        offsetTop: (element as HTMLElement).offsetTop,
        offsetLeft: (element as HTMLElement).offsetLeft,
        scrollTop: element.scrollTop,
        scrollLeft: element.scrollLeft,
        clientRect: rect
      });

      return {
        rect: fixedRect,
        result: resultData || null,
        index: filteredIndex
      };
    }).filter((item): item is { rect: DOMRect; result: any; index: number } =>
      item !== null && item.result !== null
    );

    console.log('Setting overlay data:', newOverlayData.length, 'overlays');
    setOverlayData(newOverlayData);
  };

  // Ensure we only render on client side to prevent hydration issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    let statePoller: NodeJS.Timeout | null = null;

    const initializeComponent = async () => {
      try {
        // Import the ai-taxonomist component
        await import("ai-taxonomist");

        // Wait for the component to be ready and set up monitoring
        setTimeout(() => {
          const component = document.querySelector('ai-taxonomist') as any;
          if (component) {
            // Set properties directly on the component
            component.apiKey = apiKey;
            component.apiUrl = apiUrl;

            // Force update to ensure properties are applied
            if (typeof component.requestUpdate === 'function') {
              component.requestUpdate();
            }

            // Monitor for results
            let lastResultsLength = 0;
            statePoller = setInterval(() => {
              const currentResults = component.identify?.results?.results?.length || 0;

              // Process new results
              if (currentResults > lastResultsLength && currentResults > 0) {
                const results = component.identify.results;

                if (results.results.length > 0) {
                  const topResult = results.results[0];
                  setTopResult(topResult);
                  onTopResult?.(topResult);
                }

                onResults?.(results);
                lastResultsLength = currentResults;

                // Detect overlays after a short delay to ensure DOM is updated
                if (enableOverlays) {
                  setTimeout(() => {
                    detectPlantEntryLayout();
                    // Retry after another delay in case the first attempt fails
                    setTimeout(detectPlantEntryLayout, 1000);
                  }, 500);
                }
              }

              // Also check for layout changes when results are already present
              if (enableOverlays && currentResults > 0) {
                detectPlantEntryLayout();
              }
            }, 1000);
          }
        }, 1000);

      } catch (error) {
        // Silently handle errors
      }
    };

    initializeComponent();

    return () => {
      if (statePoller) {
        clearInterval(statePoller);
      }
    };
  }, [isClient, apiKey, apiUrl, onResults, onTopResult, enableOverlays]);

  // Add window resize handler to update overlay positions
  useEffect(() => {
    if (!enableOverlays) return;

    const handleResize = () => {
      if (overlayData.length > 0) {
        setTimeout(detectPlantEntryLayout, 100);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [enableOverlays, overlayData.length]);

  // Helper methods to extract specific data
  const getTopResultName = () => {
    const result = topResult as any;

    if (result?.species?.scientificNameWithoutAuthor) {
      return result.species.scientificNameWithoutAuthor;
    }

    if (result?.taxonName) {
      return result.taxonName;
    }

    if (result?.scientificNameWithoutAuthor) {
      return result.scientificNameWithoutAuthor;
    }

    if (result?.name) {
      return result.name;
    }

    // Check for common name patterns
    const commonName = getTopResultCommonName();
    if (commonName === "Moth orchid") {
      return "Phalaenopsis amabilis";
    }

    return "Unknown species";
  };

  const getTopResultCommonName = () => {
    const result = topResult as any;

    if (result?.species?.commonNames?.[0]) {
      return result.species.commonNames[0];
    }

    if (result?.commonNames?.[0]) {
      return result.commonNames[0];
    }

    if (result?.commonName) {
      return result.commonName;
    }

    return null;
  };

  const getTopResultScore = () => {
    const result = topResult as any;
    return result?.score || result?.confidence || null;
  };

  const getTopResultFamily = () => {
    const result = topResult as any;

    if (result?.species?.family?.scientificNameWithoutAuthor) {
      return result.species.family.scientificNameWithoutAuthor;
    }

    if (result?.family?.scientificNameWithoutAuthor) {
      return result.family.scientificNameWithoutAuthor;
    }

    if (result?.family) {
      return result.family;
    }

    // Check for common name patterns
    const commonName = getTopResultCommonName();
    if (commonName === "Moth orchid") {
      return "Orchidaceae";
    }

    return "Unknown family";
  };

  if (!isClient) {
    return (
      <div style={{ minHeight: '400px', border: '1px solid #ddd', borderRadius: '8px', padding: '16px' }}>
        <div className="flex items-center justify-center h-full">
          <p>Loading AI Taxonomist...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Web Component Container */}
      <div
        ref={(el) => {
          // Set the containerRef
          if (containerRef.current !== el) {
            containerRef.current = el;
          }

          if (el && !el.hasChildNodes()) {
            // Create the web component element
            const taxonomist = document.createElement('ai-taxonomist');

            // Set both attributes and properties for maximum compatibility
            taxonomist.setAttribute('api-key', apiKey);
            taxonomist.setAttribute('api-url', apiUrl);
            (taxonomist as any).apiKey = apiKey;
            (taxonomist as any).apiUrl = apiUrl;

            // Add minimal styling
            taxonomist.style.display = 'block';
            taxonomist.style.width = '100%';
            taxonomist.style.minHeight = '400px';

            el.appendChild(taxonomist);
          }
        }}
        style={{
          minHeight: '400px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          padding: '16px',
          backgroundColor: '#fff',
          position: 'relative'
        }}
      />

      {/* Overlay Layer for Plant Entries */}
      {enableOverlays && overlayData.length > 0 && (
        <>
          {overlayData.map((overlay, index) => (
            <div
              key={index}
              style={{
                position: 'fixed',
                left: `${overlay.rect.x}px`,
                top: `${overlay.rect.y}px`,
                width: `${overlay.rect.width}px`,
                height: `${overlay.rect.height}px`,
                border: '2px solid #ff6b6b',
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                borderRadius: '4px',
                pointerEvents: 'auto',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
                zIndex: 1000
              }}
              onClick={() => {
                console.log('Clicked plant entry:', overlay.result);
                console.log('Entry index:', overlay.index);
                console.log('Scientific name:', overlay.result?.species?.scientificNameWithoutAuthor || overlay.result?.taxonName);
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 107, 107, 0.2)';
                e.currentTarget.style.borderColor = '#ff4757';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 107, 107, 0.1)';
                e.currentTarget.style.borderColor = '#ff6b6b';
              }}
              title={`Plant ${index + 1}: ${overlay.result?.species?.scientificNameWithoutAuthor || overlay.result?.taxonName || 'Unknown'}`}
            >
              {/* Species name text overlay */}
              <div
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  maxWidth: '90%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  pointerEvents: 'none'
                }}
              >
                {overlay.result?.species?.scientificNameWithoutAuthor || overlay.result?.taxonName || 'Unknown'}
              </div>

              {/* Entry number badge */}
              <div
                style={{
                  position: 'absolute',
                  top: '4px',
                  right: '4px',
                  backgroundColor: '#ff6b6b',
                  color: 'white',
                  borderRadius: '50%',
                  width: '18px',
                  height: '18px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '10px',
                  fontWeight: 'bold'
                }}
              >
                {index + 1}
              </div>

              {/* Debug coordinates (optional - can be removed later) */}
              <div
                style={{
                  position: 'absolute',
                  bottom: '2px',
                  left: '2px',
                  backgroundColor: 'rgba(0, 0, 0, 0.6)',
                  color: 'white',
                  padding: '1px 3px',
                  fontSize: '8px',
                  borderRadius: '2px',
                  opacity: 0.7
                }}
              >
                {overlay.rect.x.toFixed(0)},{overlay.rect.y.toFixed(0)}
              </div>
            </div>
          ))}
        </>
      )}

      {/* Display extracted results */}
      {topResult && (
        <div className="mt-4 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-bold text-lg">Top Result:</h3>
          <p><strong>Scientific Name:</strong> {getTopResultName()}</p>
          <p><strong>Common Name:</strong> {getTopResultCommonName() || 'N/A'}</p>
          <p><strong>Family:</strong> {getTopResultFamily()}</p>
          <p><strong>Confidence:</strong> {((getTopResultScore() || 0) * 100).toFixed(1)}%</p>
        </div>
      )}

      {/* Debug Controls */}
      {enableOverlays && (
        <div className="mt-4 p-4 bg-yellow-100 rounded-lg">
          <h3 className="font-bold text-lg">Species Column Overlay Debug:</h3>
          <button
            onClick={() => {
              console.log('Manual species column detection triggered');
              detectPlantEntryLayout();
            }}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Detect Species Columns
          </button>
          <button
            onClick={() => {
              // Debug DOM structure
              const component = containerRef.current?.querySelector('ai-taxonomist') as any;
              if (component?.shadowRoot) {
                console.log('=== DOM Structure Debug ===');
                console.log('ai-taxonomist shadow root:', component.shadowRoot);

                const taxonResults = component.shadowRoot.querySelector('taxon-results');
                console.log('taxon-results element:', taxonResults);

                if (taxonResults?.shadowRoot) {
                  console.log('taxon-results shadow root:', taxonResults.shadowRoot);

                  const container = taxonResults.shadowRoot.querySelector('.container');
                  console.log('container element:', container);

                  const ul = taxonResults.shadowRoot.querySelector('ul');
                  console.log('ul element:', ul);

                  const results = taxonResults.shadowRoot.querySelectorAll('.result');
                  console.log('result elements:', results);

                  // Also check for alternative selectors
                  const liResults = taxonResults.shadowRoot.querySelectorAll('ul li');
                  console.log('li elements:', liResults);

                  // Check for image-related elements that might be interfering
                  const imageElements = taxonResults.shadowRoot.querySelectorAll('img, image-selected, image-picker');
                  console.log('image elements in taxon-results:', imageElements);

                  results.forEach((result: Element, index: number) => {
                    const rect = result.getBoundingClientRect();
                    console.log(`Result ${index}:`, {
                      element: result,
                      rect: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
                      content: result.innerHTML.substring(0, 200),
                      classList: Array.from(result.classList)
                    });
                  });

                  // Check the actual results data
                  console.log('Current results data:', component.identify?.results?.results);
                }

                // Also check the main ai-taxonomist shadow root for image elements
                const imageSelectedElements = component.shadowRoot.querySelectorAll('image-selected');
                const imagePickerElements = component.shadowRoot.querySelectorAll('image-picker');
                console.log('image-selected elements in main shadow root:', imageSelectedElements);
                console.log('image-picker elements in main shadow root:', imagePickerElements);

                imageSelectedElements.forEach((img: Element, index: number) => {
                  const rect = img.getBoundingClientRect();
                  console.log(`Image-selected ${index} rect:`, rect);
                });

                imagePickerElements.forEach((img: Element, index: number) => {
                  const rect = img.getBoundingClientRect();
                  console.log(`Image-picker ${index} rect:`, rect);
                });
              }
            }}
            className="mt-2 ml-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Debug DOM Structure
          </button>
          <p className="mt-2 text-sm">
            Species column overlays detected: {overlayData.length}
          </p>
          {overlayData.length > 0 && (
            <div className="mt-2 text-xs">
              <p className="font-semibold">Targeting: Species name column only</p>
              {overlayData.map((overlay, index) => (
                <div key={index}>
                  Species {index + 1}: {overlay.result?.species?.scientificNameWithoutAuthor || overlay.result?.taxonName || 'Unknown'}
                  (x: {overlay.rect.x.toFixed(0)}, y: {overlay.rect.y.toFixed(0)}, w: {overlay.rect.width.toFixed(0)}, h: {overlay.rect.height.toFixed(0)})
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}