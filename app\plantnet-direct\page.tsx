'use client';

import React, { useState } from 'react';
import PlantNetIdentifier from '../components/PlantNetIdentifier';

interface PlantResult {
  score: number;
  species: {
    scientificNameWithoutAuthor: string;
    scientificNameAuthorship: string;
    scientificName: string;
    commonNames?: string[];
    family: {
      scientificNameWithoutAuthor: string;
      scientificName: string;
    };
  };
  images?: Array<{
    url: {
      o: string;
      m: string;
      s: string;
    };
    author: string;
    license: string;
  }>;
}

export default function PlantNetDirectPage() {
  const [identificationResults, setIdentificationResults] = useState<PlantResult[]>([]);

  const handleResults = (results: PlantResult[]) => {
    setIdentificationResults(results);
    console.log('Plant identification results:', results);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Direct PlantNet API Integration
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Upload plant images to identify species using the PlantNet API directly. 
            Get the top 3 results with reference images, scientific names, and confidence scores.
          </p>
        </div>

        <PlantNetIdentifier onResults={handleResults} />

        {/* Additional Information Section */}
        <div className="mt-12 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">
              How It Works
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-green-700 mb-2">Features:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Direct PlantNet API integration</li>
                  <li>• Upload up to 5 images per identification</li>
                  <li>• Specify plant parts (leaf, flower, fruit, bark)</li>
                  <li>• Get top 3 most likely species</li>
                  <li>• View reference images for each result</li>
                  <li>• See confidence scores and detailed information</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-green-700 mb-2">Tips for Best Results:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Use clear, well-lit photos</li>
                  <li>• Include different plant parts when possible</li>
                  <li>• Specify the correct organ type for each image</li>
                  <li>• Avoid blurry or heavily filtered images</li>
                  <li>• Focus on distinctive features</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        {identificationResults.length > 0 && (
          <div className="mt-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">
                Identification Summary
              </h2>
              <div className="space-y-2">
                {identificationResults.map((result, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                    <div>
                      <span className="font-medium text-green-700">
                        {result.species.scientificNameWithoutAuthor}
                      </span>
                      {result.species.commonNames && result.species.commonNames[0] && (
                        <span className="text-gray-500 ml-2">
                          ({result.species.commonNames[0]})
                        </span>
                      )}
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      {(result.score * 100).toFixed(1)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
