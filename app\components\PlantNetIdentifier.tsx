'use client';

import React, { useState } from 'react';

interface PlantResult {
  score: number;
  species: {
    scientificNameWithoutAuthor: string;
    scientificNameAuthorship: string;
    scientificName: string;
    commonNames?: string[];
    family: {
      scientificNameWithoutAuthor: string;
      scientificName: string;
    };
  };
  images?: Array<{
    url: {
      o: string;
      m: string;
      s: string;
    };
    author: string;
    license: string;
  }>;
}

interface PlantNetResponse {
  results: PlantResult[];
  bestMatch: string;
  version: string;
  remainingIdentificationRequests: number;
}

interface PlantNetIdentifierProps {
  apiKey?: string;
  apiUrl?: string;
  onResults?: (results: PlantResult[]) => void;
}

export default function PlantNetIdentifier({
  apiKey = "2b10ZGZjBNw01mCeNDfdCByQe",
  apiUrl = "https://my-api.plantnet.org/v2/identify/all",
  onResults
}: PlantNetIdentifierProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [organs, setOrgans] = useState<string[]>([]);
  const [results, setResults] = useState<PlantResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const organOptions = [
    { value: 'auto', label: 'Auto-detect' },
    { value: 'leaf', label: 'Leaf' },
    { value: 'flower', label: 'Flower' },
    { value: 'fruit', label: 'Fruit' },
    { value: 'bark', label: 'Bark' }
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 5) {
      setError('Maximum 5 images allowed');
      return;
    }
    
    setSelectedFiles(files);
    setOrgans(files.map(() => 'auto')); // Default to auto-detect
    setError(null);
  };

  const handleOrganChange = (index: number, organ: string) => {
    const newOrgans = [...organs];
    newOrgans[index] = organ;
    setOrgans(newOrgans);
  };

  const identifyPlant = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select at least one image');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      
      // Add images
      selectedFiles.forEach((file) => {
        formData.append('images', file);
      });

      // Add organs
      organs.forEach((organ) => {
        formData.append('organs', organ);
      });

      // Build URL with parameters
      const url = new URL(apiUrl);
      url.searchParams.append('api-key', apiKey);
      url.searchParams.append('include-related-images', 'true');
      url.searchParams.append('nb-results', '3'); // Limit to 3 results
      url.searchParams.append('lang', 'en');

      const response = await fetch(url.toString(), {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data: PlantNetResponse = await response.json();
      
      // Take only top 3 results
      const top3Results = data.results.slice(0, 3);
      setResults(top3Results);
      onResults?.(top3Results);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during identification');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setSelectedFiles([]);
    setOrgans([]);
    setResults([]);
    setError(null);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-center text-green-700">
        Plant Identification
      </h2>

      {/* File Upload Section */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Plant Images (up to 5)
        </label>
        <input
          type="file"
          multiple
          accept="image/jpeg,image/png"
          onChange={handleFileSelect}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
        />
        <p className="text-xs text-gray-500 mt-1">
          Supported formats: JPEG, PNG. Maximum file size: 50MB total.
        </p>
      </div>

      {/* Selected Files and Organ Selection */}
      {selectedFiles.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">Selected Images</h3>
          <div className="space-y-3">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="text-sm font-medium">{file.name}</p>
                  <p className="text-xs text-gray-500">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Plant part:
                  </label>
                  <select
                    value={organs[index] || 'auto'}
                    onChange={(e) => handleOrganChange(index, e.target.value)}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    {organOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={identifyPlant}
          disabled={loading || selectedFiles.length === 0}
          className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {loading ? 'Identifying...' : 'Identify Plant'}
        </button>
        
        {(results.length > 0 || selectedFiles.length > 0) && (
          <button
            onClick={clearResults}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50"
          >
            Clear
          </button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <p className="text-blue-700 text-sm">Analyzing images...</p>
          </div>
        </div>
      )}

      {/* Results Display */}
      {results.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-xl font-bold text-gray-800">
            Top 3 Identification Results
          </h3>
          
          {results.map((result, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6 bg-gray-50">
              <div className="flex items-start space-x-4">
                {/* Result Images */}
                <div className="flex-shrink-0">
                  {result.images && result.images.length > 0 ? (
                    <div className="grid grid-cols-2 gap-2 w-32">
                      {result.images.slice(0, 4).map((image, imgIndex) => (
                        <img
                          key={imgIndex}
                          src={image.url.s} // Use small size for thumbnails
                          alt={`${result.species.scientificNameWithoutAuthor} reference`}
                          className="w-14 h-14 object-cover rounded border"
                          title={`Photo by ${image.author} (${image.license})`}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="w-32 h-32 bg-gray-200 rounded flex items-center justify-center">
                      <span className="text-gray-500 text-xs">No images</span>
                    </div>
                  )}
                </div>

                {/* Result Information */}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-lg font-bold text-green-700">
                      {result.species.scientificNameWithoutAuthor}
                    </h4>
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      {(result.score * 100).toFixed(1)}% confidence
                    </span>
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600">
                    {result.species.commonNames && result.species.commonNames.length > 0 && (
                      <p>
                        <span className="font-medium">Common name:</span>{' '}
                        {result.species.commonNames[0]}
                      </p>
                    )}
                    
                    <p>
                      <span className="font-medium">Family:</span>{' '}
                      {result.species.family.scientificNameWithoutAuthor}
                    </p>
                    
                    <p>
                      <span className="font-medium">Full scientific name:</span>{' '}
                      {result.species.scientificName}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
