'use client';

import React, { useState } from 'react';

interface PlantResult {
  score: number;
  species: {
    scientificNameWithoutAuthor: string;
    scientificNameAuthorship: string;
    scientificName: string;
    commonNames?: string[];
    family: {
      scientificNameWithoutAuthor: string;
      scientificName: string;
    };
  };
  images?: Array<{
    url: {
      o: string;
      m: string;
      s: string;
    };
    author: string;
    license: string;
  }>;
}

interface PlantNetResponse {
  results: PlantResult[];
  bestMatch: string;
  version: string;
  remainingIdentificationRequests: number;
}

interface PlantNetIdentifierProps {
  apiKey?: string;
  apiUrl?: string;
  onResults?: (results: PlantResult[]) => void;
}

export default function PlantNetIdentifier({
  apiKey = "2b10ZGZjBNw01mCeNDfdCByQe",
  apiUrl = "https://my-api.plantnet.org/v2/identify/all",
  onResults
}: PlantNetIdentifierProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [organs, setOrgans] = useState<string[]>([]);
  const [results, setResults] = useState<PlantResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);

  const organOptions = [
    { value: 'auto', label: 'Auto-detect' },
    { value: 'leaf', label: 'Leaf' },
    { value: 'flower', label: 'Flower' },
    { value: 'fruit', label: 'Fruit' },
    { value: 'bark', label: 'Bark' }
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 5) {
      setError('Maximum 5 images allowed');
      return;
    }

    setSelectedFiles(files);
    setOrgans(files.map(() => 'auto')); // Default to auto-detect
    setError(null);

    // Create preview URLs for uploaded images
    const urls = files.map(file => URL.createObjectURL(file));
    setUploadedImageUrls(urls);
  };

  const handleOrganChange = (index: number, organ: string) => {
    const newOrgans = [...organs];
    newOrgans[index] = organ;
    setOrgans(newOrgans);
  };

  const identifyPlant = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select at least one image');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      
      // Add images
      selectedFiles.forEach((file) => {
        formData.append('images', file);
      });

      // Add organs
      organs.forEach((organ) => {
        formData.append('organs', organ);
      });

      // Build URL with parameters
      const url = new URL(apiUrl);
      url.searchParams.append('api-key', apiKey);
      url.searchParams.append('include-related-images', 'true');
      url.searchParams.append('nb-results', '3'); // Limit to 3 results
      url.searchParams.append('lang', 'en');

      const response = await fetch(url.toString(), {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data: PlantNetResponse = await response.json();
      
      // Take only top 3 results
      const top3Results = data.results.slice(0, 3);
      setResults(top3Results);
      onResults?.(top3Results);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during identification');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setSelectedFiles([]);
    setOrgans([]);
    setResults([]);
    setError(null);

    // Clean up uploaded image URLs
    uploadedImageUrls.forEach(url => URL.revokeObjectURL(url));
    setUploadedImageUrls([]);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-center text-green-700">
        Plant Identification
      </h2>

      {/* File Upload Section - Only show when no files selected */}
      {selectedFiles.length === 0 && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Plant Images (up to 5)
          </label>
          <input
            type="file"
            multiple
            accept="image/jpeg,image/png"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
          />
          <p className="text-xs text-gray-500 mt-1">
            Supported formats: JPEG, PNG. Maximum file size: 50MB total.
          </p>
        </div>
      )}

      {/* Main Content Area - After Upload */}
      {uploadedImageUrls.length > 0 && (
        <div className="mb-6">
          <div className="flex items-center justify-center">
            {/* Uploaded Images - Absolutely Centered */}
            <div className="flex-1 flex justify-center">
              {uploadedImageUrls.length === 1 ? (
                <img
                  src={uploadedImageUrls[0]}
                  alt="Uploaded plant image"
                  className="w-48 h-48 object-cover rounded-lg border-2 border-green-200"
                />
              ) : (
                <div className="flex space-x-2 justify-center">
                  {uploadedImageUrls.slice(0, 3).map((url, index) => (
                    <img
                      key={index}
                      src={url}
                      alt={`Uploaded plant image ${index + 1}`}
                      className="w-32 h-32 object-cover rounded-lg border-2 border-green-200"
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Controls Section - Right Side */}
            <div className="flex-shrink-0 ml-8 space-y-4">
              {/* Minimal Plant Part Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Plant part:
                </label>
                <select
                  value={organs[0] || 'auto'}
                  onChange={(e) => handleOrganChange(0, e.target.value)}
                  className="text-sm border border-gray-300 rounded px-3 py-2 bg-white"
                >
                  {organOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <button
                  onClick={identifyPlant}
                  disabled={loading}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {loading ? 'Identifying...' : 'Identify Plant'}
                </button>

                <button
                  onClick={clearResults}
                  className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50"
                >
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <p className="text-blue-700 text-sm">Analyzing images...</p>
          </div>
        </div>
      )}

      {/* Results Display */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-gray-800">
            Top 3 Identification Results
          </h3>

          {results.map((result, index) => (
            <div key={index} className="relative border border-gray-200 rounded-lg p-1 bg-gray-50 min-h-[120px]">
              {/* Confidence Badge - Top Left Overlay */}
              <div className="absolute top-2 left-2 z-10 bg-green-600 text-white px-3 py-2 rounded text-base font-bold">
                {Math.round(result.score * 100)}%
              </div>

              <div className="flex items-center space-x-2 h-full">
                {/* Result Images - Single Row, Larger, No Borders */}
                <div className="flex-shrink-0">
                  {result.images && result.images.length > 0 ? (
                    <div className="flex space-x-1">
                      {result.images.slice(0, 3).map((image, imgIndex) => (
                        <img
                          key={imgIndex}
                          src={image.url.m} // Use medium size for better quality
                          alt={`${result.species.scientificNameWithoutAuthor} reference`}
                          className="w-40 h-40 object-cover rounded hover:scale-150 hover:z-30 transition-transform duration-200 cursor-pointer relative"
                          title={`Photo by ${image.author} (${image.license})`}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="w-40 h-40 bg-gray-200 rounded flex items-center justify-center">
                      <span className="text-gray-500 text-sm">No images</span>
                    </div>
                  )}
                </div>

                {/* Plant Name Column */}
                <div className="flex-1 py-2 px-2">
                  <h4 className="text-2xl font-bold text-green-700 mb-2">
                    {result.species.scientificNameWithoutAuthor}
                  </h4>

                  {result.species.commonNames && result.species.commonNames.length > 0 && (
                    <p className="text-lg text-gray-600 mb-2">
                      {result.species.commonNames[0]}
                    </p>
                  )}

                  <p className="text-sm text-gray-500">
                    {result.species.family.scientificNameWithoutAuthor}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
