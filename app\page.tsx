'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';

// Dynamic import with no SSR to prevent hydration issues
const ClientAITaxonomistWrapper = dynamic(
  () => import('./components/ClientAITaxonomistWrapper'),
  {
    ssr: false,
    loading: () => <div className="p-8 text-center">Loading AI Taxonomist...</div>
  }
);

// Using flexible 'any' type to handle various API response structures

export default function Home() {
  const [selectedPlant, setSelectedPlant] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  const [plantFamily, setPlantFamily] = useState<string>('');
  const [commonName, setCommonName] = useState<string>('');
  const [allResults, setAllResults] = useState<any[]>([]);

  const handleResults = (results: any) => {
    setAllResults(results.results || []);
  };

  const handleTopResult = (result: any) => {
    // Handle different possible result structures
    const scientificName = result?.species?.scientificNameWithoutAuthor ||
                          result?.taxonName ||
                          result?.scientificNameWithoutAuthor ||
                          'Unknown';

    const commonName = result?.species?.commonNames?.[0] ||
                      result?.commonNames?.[0] ||
                      result?.commonName ||
                      'Unknown';

    const family = result?.species?.family?.scientificNameWithoutAuthor ||
                  result?.family?.scientificNameWithoutAuthor ||
                  result?.family ||
                  'Unknown';

    const score = result?.score || result?.confidence || 0;

    setSelectedPlant(scientificName);
    setConfidence(score);
    setPlantFamily(family);
    setCommonName(commonName);
  };

  const selectSpecificResult = (index: number) => {
    if (allResults[index]) {
      const result = allResults[index] as any;

      const scientificName = result?.species?.scientificNameWithoutAuthor ||
                            result?.taxonName ||
                            result?.scientificNameWithoutAuthor ||
                            'Unknown';

      const commonName = result?.species?.commonNames?.[0] ||
                        result?.commonNames?.[0] ||
                        result?.commonName ||
                        'Unknown';

      const family = result?.species?.family?.scientificNameWithoutAuthor ||
                    result?.family?.scientificNameWithoutAuthor ||
                    result?.family ||
                    'Unknown';

      const score = result?.score || result?.confidence || 0;

      setSelectedPlant(scientificName);
      setConfidence(score);
      setPlantFamily(family);
      setCommonName(commonName);
    }
  };

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <h1 className="text-4xl font-bold mb-8">
        AI-Taxonomist WebComponent POC
      </h1>
      
      <div className="w-full max-w-4xl">
        <ClientAITaxonomistWrapper
          onResults={handleResults}
          onTopResult={handleTopResult}
          enableOverlays={false}
        />
        
        {/* Display programmatically selected data */}
        {selectedPlant && (
          <div className="mt-8 p-6 bg-blue-50 rounded-lg">
            <h2 className="text-2xl font-bold mb-4">Selected Plant Information</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Scientific Name:</strong> {selectedPlant}
              </div>
              <div>
                <strong>Common Name:</strong> {commonName}
              </div>
              <div>
                <strong>Family:</strong> {plantFamily}
              </div>
              <div>
                <strong>Confidence:</strong> {(confidence * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        )}

        {/* Display all results with selection buttons */}
        {allResults.length > 0 && (
          <div className="mt-8 p-6 bg-gray-50 rounded-lg">
            <h2 className="text-2xl font-bold mb-4">All Results</h2>
            <div className="space-y-4">
              {allResults.slice(0, 5).map((result: any, index) => {
                const scientificName = result?.species?.scientificNameWithoutAuthor ||
                                      result?.taxonName ||
                                      result?.scientificNameWithoutAuthor ||
                                      'Unknown';

                const commonName = result?.species?.commonNames?.[0] ||
                                  result?.commonNames?.[0] ||
                                  result?.commonName ||
                                  'No common name';

                const family = result?.species?.family?.scientificNameWithoutAuthor ||
                              result?.family?.scientificNameWithoutAuthor ||
                              result?.family ||
                              'Unknown';

                const score = result?.score || result?.confidence || 0;

                return (
                  <div key={index} className="flex items-center justify-between p-4 bg-white rounded border">
                    <div className="flex-1">
                      <div className="font-semibold">{scientificName}</div>
                      <div className="text-sm text-gray-600">
                        {commonName} |
                        Family: {family} |
                        Confidence: {(score * 100).toFixed(1)}%
                      </div>
                    </div>
                    <button
                      onClick={() => selectSpecificResult(index)}
                      className="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                      Select This
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </main>
  );
}