<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Taxonomist Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        ai-taxonomist {
            display: block;
            width: 100%;
            min-height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            background-color: #fff;
        }
        .debug {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI Taxonomist Component Test</h1>
        
        <ai-taxonomist 
            api-key="2b10ZGZjBNw01mCeNDfdCByQe"
            api-url="https://my-api.plantnet.org/v2/identify/all">
        </ai-taxonomist>
        
        <div class="debug">
            <h3>Debug Info</h3>
            <p>Component should appear above this section.</p>
            <p>Check browser console for detailed logs.</p>
            <button onclick="checkComponent()">Check Component State</button>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <h3>Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script type="module">
        // Import the ai-taxonomist component
        import 'ai-taxonomist';
        
        console.log('AI Taxonomist module loaded');
        
        // Wait for component to be ready
        window.addEventListener('DOMContentLoaded', () => {
            const component = document.querySelector('ai-taxonomist');
            console.log('Component found:', !!component);
            
            if (component) {
                console.log('Component properties:', {
                    apiKey: component.apiKey,
                    apiUrl: component.apiUrl,
                    identify: component.identify
                });
                
                // Listen for state changes
                const checkState = () => {
                    console.log('Current state:', component.identify?.state);
                    console.log('Current results:', component.identify?.results);
                    
                    if (component.identify?.state === 2 && component.identify?.results?.results?.length > 0) {
                        displayResults(component.identify.results);
                    }
                };
                
                // Poll for changes
                setInterval(checkState, 1000);
                
                // Try to trigger update
                setTimeout(() => {
                    if (typeof component.requestUpdate === 'function') {
                        component.requestUpdate();
                        console.log('Triggered requestUpdate');
                    }
                }, 1000);
            }
        });
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('results-content');
            
            if (results.results && results.results.length > 0) {
                const topResult = results.results[0];
                contentDiv.innerHTML = `
                    <p><strong>Scientific Name:</strong> ${topResult.species?.scientificNameWithoutAuthor || topResult.taxonName || 'Unknown'}</p>
                    <p><strong>Common Name:</strong> ${topResult.species?.commonNames?.[0] || topResult.commonNames?.[0] || 'N/A'}</p>
                    <p><strong>Family:</strong> ${topResult.species?.family?.scientificNameWithoutAuthor || topResult.family || 'Unknown'}</p>
                    <p><strong>Confidence:</strong> ${((topResult.score || 0) * 100).toFixed(1)}%</p>
                `;
                resultsDiv.style.display = 'block';
            }
        }
        
        // Make function global for button
        window.checkComponent = function() {
            const component = document.querySelector('ai-taxonomist');
            console.log('=== Component State Check ===');
            console.log('Component exists:', !!component);
            if (component) {
                console.log('API Key:', component.apiKey);
                console.log('API URL:', component.apiUrl);
                console.log('Identify state:', component.identify);
                console.log('Image files:', component.imageFiles);
                console.log('Shadow root:', component.shadowRoot);
                if (component.shadowRoot) {
                    console.log('Shadow root HTML:', component.shadowRoot.innerHTML);
                }
            }
        };
    </script>
</body>
</html>
