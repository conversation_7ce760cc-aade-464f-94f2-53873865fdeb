<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Taxonomist Test</title>
</head>
<body>
    <h1>AI Taxonomist Web Component Test</h1>
    <div id="status">Loading...</div>
    
    <div id="component-container" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
        <ai-taxonomist api-key="test-key">
            <template slot="attachments">
                <button>Test Button</button>
            </template>
        </ai-taxonomist>
    </div>

    <script type="module">
        console.log('Starting test...');
        
        try {
            // Try to import the component
            await import('/node_modules/ai-taxonomist/dist/src/index.js');
            console.log('Component imported successfully');
            
            // Check if custom element is defined
            const isDefined = customElements.get('ai-taxonomist');
            console.log('Custom element defined:', !!isDefined);
            
            document.getElementById('status').textContent = 
                isDefined ? 'Component loaded successfully!' : 'Component loaded but not defined';
                
        } catch (error) {
            console.error('Error loading component:', error);
            document.getElementById('status').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
