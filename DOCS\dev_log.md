
## Development Standards

    - **Documentation Policy:** Update documentation in this `DOCS/` folder at each milestone completion
    - **Code Style:** 2-space indentation for all files
    - **Environment:** `uv` for Python, Windows-compatible development
        - always prioritize using Python script even for the execution of code/shell commands
    - **Unicode:** Critical for Japanese text processing

